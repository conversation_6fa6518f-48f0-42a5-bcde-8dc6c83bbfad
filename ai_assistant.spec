# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ai_assistant.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('img/*.ico', 'img'),  # 包含图标文件
        ('config.json', '.'),   # 包含配置文件
    ],
    hiddenimports=[
        'tkinter',
        'PIL._tkinter_finder',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AI助手',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='img/Google AI Studio.ico'  # 设置程序图标
)