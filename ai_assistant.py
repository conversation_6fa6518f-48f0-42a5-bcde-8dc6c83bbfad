import tkinter as tk
from tkinter import ttk, messagebox
import pystray
from PIL import Image
import keyboard
import pyperclip
import win32gui
import asyncio
import aiohttp
import json
from typing import Optional, Callable
import threading
import os
from settings_window import SettingsWindow

class AIService:
    def __init__(self):
        self.current_request = None
        self.session = None
        self.config = self.load_config()
        
    @staticmethod
    def load_config():
        return SettingsWindow.load_config()
        
    async def init_session(self):
        if not self.session:
            self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=120))
            
    async def close_session(self):
        if self.session:
            await self.session.close()
            self.session = None
            
    async def process_request(self, prompt: str, selected_text: Optional[str] = None, 
                            progress_callback: Optional[Callable[[str], None]] = None):
        try:
            await self.init_session()
            
            # 获取当前选择的模型配置
            selected_model = self.config.get("selected_model", "DeepSeek")
            
            # 根据选择的模型确定API配置
            api_config = {
                "DeepSeek": {
                    "url": "https://api.deepseek.com/v1/chat/completions",
                    "key": self.config.get("deepseek_key", ""),
                    "model": self.config.get("deepseek_model", "deepseek-chat")
                },
                "Kimi": {
                    "url": "https://api.moonshot.cn/v1/chat/completions",
                    "key": self.config.get("kimi_key", ""),
                    "model": self.config.get("kimi_model", "moonshot-v1-8k")
                },
                "ChatGPT": {
                    "url": "https://api.ephone.ai/v1/chat/completions",
                    "key": self.config.get("chatgpt_key", ""),
                    "model": self.config.get("chatgpt_model", "claude-3-5-sonnet-20241022")
                },
                "QianWen": {
                    "url": "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
                    "key": self.config.get("qianwen_key", ""),
                    "model": self.config.get("qianwen_model", "qwen-plus")
                }
            }
            
            current_config = api_config.get(selected_model)
            if not current_config:
                raise Exception("未选择有效的模型")
                
            if not current_config["key"]:
                raise Exception(f"请先设置 {selected_model} 的 API Key")
            
            # 构建消息
            messages = []
            if selected_text:
                prompt = f"{prompt}\n\n基于以下文本处理前面的需求: {selected_text}"
                
            messages.append({
                "role": "user",
                "content": prompt
            })
            
            # 发送请求
            headers = {
                "Authorization": f"Bearer {current_config['key']}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": current_config["model"],
                "messages": messages,
                "stream": True
            }

            async with self.session.post(current_config["url"], json=data, headers=headers) as response:
                response.raise_for_status()
                async for line in response.content:
                    if line:
                        line = line.decode('utf-8')
                        if line.startswith('data: '):
                            if line == 'data: [DONE]':
                                break
                                
                            try:
                                json_data = json.loads(line[6:])
                                content = json_data['choices'][0]['delta'].get('content', '')
                                
                                if content and progress_callback:
                                    progress_callback(content)
                                        
                            except json.JSONDecodeError:
                                continue
                                
        except Exception as e:
            raise Exception(f"处理请求时出错: {str(e)}")

class MainWindow(tk.Tk):
    def __init__(self):
        super().__init__()
        
        self.title("AI助手")
        self.geometry("600x400")
        
        self.ai_service = AIService()
        self.is_responding = False
        self.target_window = None
        self.selected_text = None # 新增变量保存选中的文本
        
        # 创建事件循环
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        self.setup_ui()
        self.setup_tray()
        self.setup_hotkeys()
        
        # 绑定窗口失去焦点事件
        self.bind('<FocusOut>', self.on_focus_out)
        
        # 绑定ESC键事件
        self.bind('<Escape>', self.on_escape)
        
    def setup_ui(self):
        # 创建主框架
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 输入框
        self.prompt_text = tk.Text(main_frame, height=10, wrap=tk.WORD)
        self.prompt_text.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 绑定回车键发送
        self.prompt_text.bind('<Return>', lambda e: self.submit())
        
        # 按钮框
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=5)
        
        self.settings_btn = ttk.Button(button_frame, text="设置", command=self.show_settings)
        self.settings_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = ttk.Button(button_frame, text="停止", command=self.stop_response)
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        self.copy_btn = ttk.Button(button_frame, text="复制", command=self.copy_response)
        self.copy_btn.pack(side=tk.LEFT, padx=5)

        self.clear_btn = ttk.Button(button_frame, text="清空", command=self.clear_text)
        self.clear_btn.pack(side=tk.LEFT, padx=5)

        self.insert_btn = ttk.Button(button_frame, text="插入", command=self.insert_response)
        self.insert_btn.pack(side=tk.LEFT, padx=5)
        
        self.submit_btn = ttk.Button(button_frame, text="发送", command=self.submit)
        self.submit_btn.pack(side=tk.RIGHT, padx=5)
        
        # 绑定快捷键
        self.bind('<Control-Return>', lambda e: self.submit())
        
    def setup_tray(self):
        # 创建系统托盘图标
        image = Image.open(os.path.join(os.path.dirname(__file__), "img", "Google AI Studio.ico"))
        menu = pystray.Menu(
            pystray.MenuItem("显示", self.show_window),
            pystray.MenuItem("退出", self.quit_app)
        )
        self.tray_icon = pystray.Icon("AI助手", image, "AI助手", menu)
        threading.Thread(target=self.tray_icon.run, daemon=True).start()
        
    def on_focus_out(self, event):
        """窗口失去焦点时自动最小化到系统托盘"""
        # 获取当前鼠标位置
        x = self.winfo_pointerx()
        y = self.winfo_pointery()
        
        # 获取窗口边框宽度
        border_width = 8  # Windows默认边框宽度
        
        # 检查鼠标是否在窗口内(包括边框)
        if not (self.winfo_x() - border_width <= x <= self.winfo_x() + self.winfo_width() + border_width and 
                self.winfo_y() - border_width <= y <= self.winfo_y() + self.winfo_height() + border_width):
            self.withdraw()
            
    def on_escape(self, event):
        """按ESC键时清空输入框并最小化窗口"""
        self.prompt_text.delete("1.0", tk.END)
        self.withdraw()
            
    def setup_hotkeys(self):
        # 注册全局快捷键
        keyboard.add_hotkey('ctrl+shift+q', self.on_hotkey)
        
    def on_hotkey(self):
        if self.is_responding:
            self.stop_response()
            return
            
        # 获取当前窗口信息
        self.target_window = win32gui.GetForegroundWindow()
        if self.target_window:
            # 复制选中文本
            keyboard.send('ctrl+c')
            # 等待100ms后获取剪贴板内容
            self.after(100, self.get_selected_text)

    def get_selected_text(self):
        """获取选中的文本"""
        self.selected_text = pyperclip.paste()
        self.show_window()

    def copy_response(self):
        """复制AI回复到剪贴板"""
        response_text = self.prompt_text.get("1.0", tk.END).strip()
        if response_text and response_text != "[正在思考]":
            pyperclip.copy(response_text)
            messagebox.showinfo("提示", "已复制到剪贴板")

    def insert_response(self):
        """插入AI回复到原窗口"""
        response_text = self.prompt_text.get("1.0", tk.END).strip()
        if response_text and response_text != "[正在思考]":
            # 保存当前响应到剪贴板
            pyperclip.copy(response_text)
            
            # 切换到目标窗口
            if self.target_window:
                win32gui.SetForegroundWindow(self.target_window)
                # 等待窗口激活
                self.after(100, lambda: keyboard.send('ctrl+v'))
                
            self.withdraw()

    def clear_text(self):
        """清空文本框"""
        self.prompt_text.delete("1.0", tk.END)
            
    def submit(self):
        """同步方法调用异步方法"""
        asyncio.run_coroutine_threadsafe(self._async_submit(), self.loop)
        
    async def _async_submit(self):
        """异步提交实现"""
        prompt = self.prompt_text.get("1.0", tk.END).strip()
        if not prompt:
            self.after(0, lambda: messagebox.showwarning("提示", "请输入内容"))
            return
            
        self.prompt_text.delete("1.0", tk.END)
        self.prompt_text.insert("1.0", "[正在思考]")
        self.is_responding = True
        
        try:
            def progress_callback(token: str):
                if token == "[正在思考]":
                    return
                    
                def update_ui():
                    if self.prompt_text.get("1.0", tk.END).strip() == "[正在思考]":
                        self.prompt_text.delete("1.0", tk.END)
                    self.prompt_text.insert(tk.END, token)
                
                self.after(0, update_ui)
                
            await self.ai_service.process_request(
                prompt, 
                self.selected_text, # 使用保存的选中文本
                progress_callback
            )
            
        except Exception as e:
            self.after(0, lambda: messagebox.showerror("错误", str(e)))
            
        finally:
            self.is_responding = False
            self.selected_text = None # 清空选中的文本
            
    def stop_response(self):
        """停止当前响应"""
        if self.is_responding:
            asyncio.run_coroutine_threadsafe(self.ai_service.close_session(), self.loop)
            self.is_responding = False
            if self.prompt_text.get("1.0", tk.END).strip() == "[正在思考]":
                self.prompt_text.delete("1.0", tk.END)
                
    def show_window(self):
        """显示窗口"""
        self.deiconify()
        self.lift()
        self.focus_force()
        # 将光标定位到输入框
        self.prompt_text.focus_set()
        
    def show_settings(self):
        """显示设置窗口"""
        settings_window = SettingsWindow(self)
        settings_window.transient(self)
        settings_window.grab_set()
        self.wait_window(settings_window)
        # 重新加载配置
        self.ai_service.config = self.ai_service.load_config()
        
    def on_closing(self):
        """窗口关闭处理"""
        self.withdraw()
        
    def quit_app(self):
        """退出应用"""
        # 停止事件循环
        self.loop.call_soon_threadsafe(self.loop.stop)
        
        # 关闭AI服务
        if self.ai_service.session:
            self.loop.run_until_complete(self.ai_service.close_session())
            
        # 停止托盘图标
        self.tray_icon.stop()
        
        # 退出应用
        self.quit()
        
    def run(self):
        """启动应用"""
        # 在新线程中运行事件循环
        def run_loop():
            asyncio.set_event_loop(self.loop)
            self.loop.run_forever()
            
        threading.Thread(target=run_loop, daemon=True).start()
        
        # 启动主窗口
        self.mainloop()

if __name__ == "__main__":
    app = MainWindow()
    app.protocol("WM_DELETE_WINDOW", app.on_closing)
    app.run()