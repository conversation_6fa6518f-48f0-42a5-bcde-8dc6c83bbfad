(['D:\\test\\aiai\\ai_assistant.py'],
 ['D:\\test\\aiai'],
 ['tkinter', 'PIL._tkinter_finder'],
 [('C:\\Python3\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('C:\\Python3\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Python3\\Lib\\site-packages\\_pyinstaller_hooks_contrib', -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('config.json', 'D:\\test\\aiai\\config.json', 'DATA'),
  ('img\\Google AI Studio.ico',
   'D:\\test\\aiai\\img\\Google AI Studio.ico',
   'DATA')],
 '3.12.3 (tags/v3.12.3:f6650f9, Apr  9 2024, 14:05:25) [MSC v.1938 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Python3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Python3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Python3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Python3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Python3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\Python3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Python3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('ai_assistant', 'D:\\test\\aiai\\ai_assistant.py', 'PYSOURCE')],
 [('_pyi_rth_utils.qt',
   'C:\\Python3\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('importlib', 'C:\\Python3\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python3\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python3\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.abc', 'C:\\Python3\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Python3\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Python3\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Python3\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Python3\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Python3\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'C:\\Python3\\Lib\\tempfile.py', 'PYMODULE'),
  ('random', 'C:\\Python3\\Lib\\random.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python3\\Lib\\_strptime.py', 'PYMODULE'),
  ('datetime', 'C:\\Python3\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\Python3\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('calendar', 'C:\\Python3\\Lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'C:\\Python3\\Lib\\argparse.py', 'PYMODULE'),
  ('copy', 'C:\\Python3\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'C:\\Python3\\Lib\\gettext.py', 'PYMODULE'),
  ('struct', 'C:\\Python3\\Lib\\struct.py', 'PYMODULE'),
  ('statistics', 'C:\\Python3\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'C:\\Python3\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python3\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python3\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'C:\\Python3\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'C:\\Python3\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'C:\\Python3\\Lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'C:\\Python3\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'C:\\Python3\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\Python3\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'C:\\Python3\\Lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle', 'C:\\Python3\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('string', 'C:\\Python3\\Lib\\string.py', 'PYMODULE'),
  ('bisect', 'C:\\Python3\\Lib\\bisect.py', 'PYMODULE'),
  ('shutil', 'C:\\Python3\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'C:\\Python3\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'C:\\Python3\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'C:\\Python3\\Lib\\_compression.py', 'PYMODULE'),
  ('lzma', 'C:\\Python3\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'C:\\Python3\\Lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Python3\\Lib\\fnmatch.py', 'PYMODULE'),
  ('importlib._abc', 'C:\\Python3\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python3\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Python3\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Python3\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Python3\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Python3\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Python3\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Python3\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'C:\\Python3\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'C:\\Python3\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python3\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', 'C:\\Python3\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python3\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python3\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'C:\\Python3\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators', 'C:\\Python3\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'C:\\Python3\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python3\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'C:\\Python3\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'C:\\Python3\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset', 'C:\\Python3\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'C:\\Python3\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime', 'C:\\Python3\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._policybase', 'C:\\Python3\\Lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.header', 'C:\\Python3\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'C:\\Python3\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'C:\\Python3\\Lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr', 'C:\\Python3\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\Python3\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Python3\\Lib\\ipaddress.py', 'PYMODULE'),
  ('socket', 'C:\\Python3\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'C:\\Python3\\Lib\\selectors.py', 'PYMODULE'),
  ('quopri', 'C:\\Python3\\Lib\\quopri.py', 'PYMODULE'),
  ('inspect', 'C:\\Python3\\Lib\\inspect.py', 'PYMODULE'),
  ('token', 'C:\\Python3\\Lib\\token.py', 'PYMODULE'),
  ('dis', 'C:\\Python3\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\Python3\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'C:\\Python3\\Lib\\ast.py', 'PYMODULE'),
  ('contextlib', 'C:\\Python3\\Lib\\contextlib.py', 'PYMODULE'),
  ('textwrap', 'C:\\Python3\\Lib\\textwrap.py', 'PYMODULE'),
  ('zipfile', 'C:\\Python3\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'C:\\Python3\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Python3\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile', 'C:\\Python3\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.util', 'C:\\Python3\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('pathlib', 'C:\\Python3\\Lib\\pathlib.py', 'PYMODULE'),
  ('email', 'C:\\Python3\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'C:\\Python3\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'C:\\Python3\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('csv', 'C:\\Python3\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers', 'C:\\Python3\\Lib\\importlib\\readers.py', 'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Python3\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Python3\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize', 'C:\\Python3\\Lib\\tokenize.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python3\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Python3\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('pkg_resources',
   'C:\\Python3\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Python3\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('subprocess', 'C:\\Python3\\Lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'C:\\Python3\\Lib\\signal.py', 'PYMODULE'),
  ('packaging.metadata',
   'C:\\Python3\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Python3\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Python3\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Python3\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Python3\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Python3\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('ctypes', 'C:\\Python3\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes.wintypes', 'C:\\Python3\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('ctypes._endian', 'C:\\Python3\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('packaging._elffile',
   'C:\\Python3\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Python3\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('sysconfig', 'C:\\Python3\\Lib\\sysconfig.py', 'PYMODULE'),
  ('_aix_support', 'C:\\Python3\\Lib\\_aix_support.py', 'PYMODULE'),
  ('typing_extensions',
   'C:\\Python3\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('configparser', 'C:\\Python3\\Lib\\configparser.py', 'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('queue', 'C:\\Python3\\Lib\\queue.py', 'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('unittest.mock', 'C:\\Python3\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest', 'C:\\Python3\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   'C:\\Python3\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals', 'C:\\Python3\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.main', 'C:\\Python3\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner', 'C:\\Python3\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.loader', 'C:\\Python3\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.suite', 'C:\\Python3\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.case', 'C:\\Python3\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest._log', 'C:\\Python3\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('difflib', 'C:\\Python3\\Lib\\difflib.py', 'PYMODULE'),
  ('unittest.result', 'C:\\Python3\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.util', 'C:\\Python3\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('concurrent.futures',
   'C:\\Python3\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Python3\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Python3\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Python3\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Python3\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Python3\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Python3\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy', 'C:\\Python3\\Lib\\runpy.py', 'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Python3\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Python3\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Python3\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Python3\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Python3\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Python3\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Python3\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Python3\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Python3\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Python3\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Python3\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Python3\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Python3\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Python3\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'C:\\Python3\\Lib\\secrets.py', 'PYMODULE'),
  ('hmac', 'C:\\Python3\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Python3\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Python3\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Python3\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Python3\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client', 'C:\\Python3\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'C:\\Python3\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.server', 'C:\\Python3\\Lib\\xmlrpc\\server.py', 'PYMODULE'),
  ('pydoc', 'C:\\Python3\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'C:\\Python3\\Lib\\webbrowser.py', 'PYMODULE'),
  ('shlex', 'C:\\Python3\\Lib\\shlex.py', 'PYMODULE'),
  ('pydoc_data.topics', 'C:\\Python3\\Lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('pydoc_data', 'C:\\Python3\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('tty', 'C:\\Python3\\Lib\\tty.py', 'PYMODULE'),
  ('socketserver', 'C:\\Python3\\Lib\\socketserver.py', 'PYMODULE'),
  ('html', 'C:\\Python3\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'C:\\Python3\\Lib\\html\\entities.py', 'PYMODULE'),
  ('http.server', 'C:\\Python3\\Lib\\http\\server.py', 'PYMODULE'),
  ('http', 'C:\\Python3\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Python3\\Lib\\mimetypes.py', 'PYMODULE'),
  ('xml.parsers.expat', 'C:\\Python3\\Lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.parsers', 'C:\\Python3\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml', 'C:\\Python3\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Python3\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils', 'C:\\Python3\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('urllib.request', 'C:\\Python3\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('getpass', 'C:\\Python3\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Python3\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'C:\\Python3\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'C:\\Python3\\Lib\\netrc.py', 'PYMODULE'),
  ('http.cookiejar', 'C:\\Python3\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('ssl', 'C:\\Python3\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response', 'C:\\Python3\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib.error', 'C:\\Python3\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('xml.sax', 'C:\\Python3\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler', 'C:\\Python3\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Python3\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader', 'C:\\Python3\\Lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('http.client', 'C:\\Python3\\Lib\\http\\client.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Python3\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Python3\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent', 'C:\\Python3\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py38',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('site', 'C:\\Python3\\Lib\\site.py', 'PYMODULE'),
  ('rlcompleter', 'C:\\Python3\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('readline', 'C:\\Python3\\Lib\\site-packages\\readline.py', 'PYMODULE'),
  ('pyreadline3.console',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('ctypes.util', 'C:\\Python3\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'C:\\Python3\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Python3\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Python3\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Python3\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Python3\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('logging.handlers', 'C:\\Python3\\Lib\\logging\\handlers.py', 'PYMODULE'),
  ('win32evtlogutil',
   'C:\\Python3\\Lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'C:\\Python3\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('win32con',
   'C:\\Python3\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('smtplib', 'C:\\Python3\\Lib\\smtplib.py', 'PYMODULE'),
  ('pyreadline3.keysyms',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('glob', 'C:\\Python3\\Lib\\glob.py', 'PYMODULE'),
  ('pyreadline3.lineeditor',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('_sitebuiltins', 'C:\\Python3\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel._setuptools_logging',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\_setuptools_logging.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.bdist_wheel',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('tomli', 'C:\\Python3\\Lib\\site-packages\\tomli\\__init__.py', 'PYMODULE'),
  ('tomli._parser',
   'C:\\Python3\\Lib\\site-packages\\tomli\\_parser.py',
   'PYMODULE'),
  ('tomli._types',
   'C:\\Python3\\Lib\\site-packages\\tomli\\_types.py',
   'PYMODULE'),
  ('tomli._re', 'C:\\Python3\\Lib\\site-packages\\tomli\\_re.py', 'PYMODULE'),
  ('tomllib', 'C:\\Python3\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'C:\\Python3\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._types', 'C:\\Python3\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tomllib._re', 'C:\\Python3\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Python3\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\Python3\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Python3\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Python3\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Python3\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Python3\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Python3\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('zipimport', 'C:\\Python3\\Lib\\zipimport.py', 'PYMODULE'),
  ('plistlib', 'C:\\Python3\\Lib\\plistlib.py', 'PYMODULE'),
  ('platform', 'C:\\Python3\\Lib\\platform.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Python3\\Lib\\pkgutil.py', 'PYMODULE'),
  ('__future__', 'C:\\Python3\\Lib\\__future__.py', 'PYMODULE'),
  ('PIL._tkinter_finder',
   'C:\\Python3\\Lib\\site-packages\\PIL\\_tkinter_finder.py',
   'PYMODULE'),
  ('stringprep', 'C:\\Python3\\Lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\Python3\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python3\\Lib\\_py_abc.py', 'PYMODULE'),
  ('settings_window', 'D:\\test\\aiai\\settings_window.py', 'PYMODULE'),
  ('threading', 'C:\\Python3\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'C:\\Python3\\Lib\\_threading_local.py', 'PYMODULE'),
  ('typing', 'C:\\Python3\\Lib\\typing.py', 'PYMODULE'),
  ('json', 'C:\\Python3\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Python3\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Python3\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Python3\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('aiohttp',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\__init__.py',
   'PYMODULE'),
  ('aiohttp.worker',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\worker.py',
   'PYMODULE'),
  ('aiohttp.web_log',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\web_log.py',
   'PYMODULE'),
  ('aiohttp.web_response',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\web_response.py',
   'PYMODULE'),
  ('aiohttp.typedefs',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\typedefs.py',
   'PYMODULE'),
  ('yarl', 'C:\\Python3\\Lib\\site-packages\\yarl\\__init__.py', 'PYMODULE'),
  ('yarl._url', 'C:\\Python3\\Lib\\site-packages\\yarl\\_url.py', 'PYMODULE'),
  ('yarl._quoting',
   'C:\\Python3\\Lib\\site-packages\\yarl\\_quoting.py',
   'PYMODULE'),
  ('yarl._quoting_py',
   'C:\\Python3\\Lib\\site-packages\\yarl\\_quoting_py.py',
   'PYMODULE'),
  ('idna', 'C:\\Python3\\Lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.package_data',
   'C:\\Python3\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Python3\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core', 'C:\\Python3\\Lib\\site-packages\\idna\\core.py', 'PYMODULE'),
  ('idna.uts46data',
   'C:\\Python3\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Python3\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('aiohttp.compression_utils',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\compression_utils.py',
   'PYMODULE'),
  ('multidict',
   'C:\\Python3\\Lib\\site-packages\\multidict\\__init__.py',
   'PYMODULE'),
  ('multidict._multidict_py',
   'C:\\Python3\\Lib\\site-packages\\multidict\\_multidict_py.py',
   'PYMODULE'),
  ('multidict._compat',
   'C:\\Python3\\Lib\\site-packages\\multidict\\_compat.py',
   'PYMODULE'),
  ('multidict._abc',
   'C:\\Python3\\Lib\\site-packages\\multidict\\_abc.py',
   'PYMODULE'),
  ('http.cookies', 'C:\\Python3\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('aiohttp.web_request',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\web_request.py',
   'PYMODULE'),
  ('aiohttp.web_urldispatcher',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\web_urldispatcher.py',
   'PYMODULE'),
  ('aiohttp.web_routedef',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\web_routedef.py',
   'PYMODULE'),
  ('aiohttp.web_fileresponse',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\web_fileresponse.py',
   'PYMODULE'),
  ('aiohttp.web_protocol',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\web_protocol.py',
   'PYMODULE'),
  ('aiohttp.web_server',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\web_server.py',
   'PYMODULE'),
  ('aiohttp.tcp_helpers',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\tcp_helpers.py',
   'PYMODULE'),
  ('aiohttp.log',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\log.py',
   'PYMODULE'),
  ('aiohttp.base_protocol',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\base_protocol.py',
   'PYMODULE'),
  ('asyncio.streams', 'C:\\Python3\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.tasks', 'C:\\Python3\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.queues', 'C:\\Python3\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.mixins', 'C:\\Python3\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.locks', 'C:\\Python3\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.timeouts', 'C:\\Python3\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.futures', 'C:\\Python3\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Python3\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Python3\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.log', 'C:\\Python3\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.protocols', 'C:\\Python3\\Lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Python3\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants', 'C:\\Python3\\Lib\\asyncio\\constants.py', 'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Python3\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events', 'C:\\Python3\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Python3\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('aiohttp.web_exceptions',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\web_exceptions.py',
   'PYMODULE'),
  ('aiohttp.http_writer',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\http_writer.py',
   'PYMODULE'),
  ('aiohttp.http_parser',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\http_parser.py',
   'PYMODULE'),
  ('aiohttp.http_exceptions',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\http_exceptions.py',
   'PYMODULE'),
  ('attr', 'C:\\Python3\\Lib\\site-packages\\attr\\__init__.py', 'PYMODULE'),
  ('attr._version_info',
   'C:\\Python3\\Lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr._next_gen',
   'C:\\Python3\\Lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._make', 'C:\\Python3\\Lib\\site-packages\\attr\\_make.py', 'PYMODULE'),
  ('attr._funcs',
   'C:\\Python3\\Lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._compat',
   'C:\\Python3\\Lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._cmp', 'C:\\Python3\\Lib\\site-packages\\attr\\_cmp.py', 'PYMODULE'),
  ('attr.validators',
   'C:\\Python3\\Lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attr.filters',
   'C:\\Python3\\Lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'C:\\Python3\\Lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.converters',
   'C:\\Python3\\Lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.setters',
   'C:\\Python3\\Lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr._config',
   'C:\\Python3\\Lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('aiohttp.abc',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\abc.py',
   'PYMODULE'),
  ('aiohttp.web_app',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\web_app.py',
   'PYMODULE'),
  ('aiohttp.web_middlewares',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\web_middlewares.py',
   'PYMODULE'),
  ('frozenlist',
   'C:\\Python3\\Lib\\site-packages\\frozenlist\\__init__.py',
   'PYMODULE'),
  ('aiosignal',
   'C:\\Python3\\Lib\\site-packages\\aiosignal\\__init__.py',
   'PYMODULE'),
  ('aiohttp.web',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\web.py',
   'PYMODULE'),
  ('aiohttp.web_ws',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\web_ws.py',
   'PYMODULE'),
  ('aiohttp.web_runner',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\web_runner.py',
   'PYMODULE'),
  ('aiohttp.tracing',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\tracing.py',
   'PYMODULE'),
  ('aiohttp.client_reqrep',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\client_reqrep.py',
   'PYMODULE'),
  ('aiohttp.connector',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\connector.py',
   'PYMODULE'),
  ('aiohttp.locks',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\locks.py',
   'PYMODULE'),
  ('aiohttp.client_proto',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\client_proto.py',
   'PYMODULE'),
  ('aiohttp.client_exceptions',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\client_exceptions.py',
   'PYMODULE'),
  ('aiohttp.streams',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\streams.py',
   'PYMODULE'),
  ('aiohttp.resolver',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\resolver.py',
   'PYMODULE'),
  ('aiohttp.payload_streamer',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\payload_streamer.py',
   'PYMODULE'),
  ('aiohttp.helpers',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\helpers.py',
   'PYMODULE'),
  ('aiohttp.formdata',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\formdata.py',
   'PYMODULE'),
  ('aiohttp.cookiejar',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\cookiejar.py',
   'PYMODULE'),
  ('aiohttp.client',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\client.py',
   'PYMODULE'),
  ('aiohttp.http_websocket',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\http_websocket.py',
   'PYMODULE'),
  ('aiohttp.client_ws',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\client_ws.py',
   'PYMODULE'),
  ('aiohttp.http',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\http.py',
   'PYMODULE'),
  ('aiohttp.multipart',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\multipart.py',
   'PYMODULE'),
  ('uuid', 'C:\\Python3\\Lib\\uuid.py', 'PYMODULE'),
  ('aiohttp.payload',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\payload.py',
   'PYMODULE'),
  ('aiohttp.hdrs',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\hdrs.py',
   'PYMODULE'),
  ('asyncio', 'C:\\Python3\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Python3\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Python3\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Python3\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Python3\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Python3\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Python3\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads', 'C:\\Python3\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Python3\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Python3\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.runners', 'C:\\Python3\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Python3\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock', 'C:\\Python3\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.staggered', 'C:\\Python3\\Lib\\asyncio\\staggered.py', 'PYMODULE'),
  ('asyncio.sslproto', 'C:\\Python3\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.transports',
   'C:\\Python3\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('pyperclip',
   'C:\\Python3\\Lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('PyQt5', 'C:\\Python3\\Lib\\site-packages\\PyQt5\\__init__.py', 'PYMODULE'),
  ('keyboard',
   'C:\\Python3\\Lib\\site-packages\\keyboard\\__init__.py',
   'PYMODULE'),
  ('keyboard._canonical_names',
   'C:\\Python3\\Lib\\site-packages\\keyboard\\_canonical_names.py',
   'PYMODULE'),
  ('keyboard._generic',
   'C:\\Python3\\Lib\\site-packages\\keyboard\\_generic.py',
   'PYMODULE'),
  ('keyboard._keyboard_event',
   'C:\\Python3\\Lib\\site-packages\\keyboard\\_keyboard_event.py',
   'PYMODULE'),
  ('keyboard._darwinkeyboard',
   'C:\\Python3\\Lib\\site-packages\\keyboard\\_darwinkeyboard.py',
   'PYMODULE'),
  ('keyboard._nixkeyboard',
   'C:\\Python3\\Lib\\site-packages\\keyboard\\_nixkeyboard.py',
   'PYMODULE'),
  ('keyboard._nixcommon',
   'C:\\Python3\\Lib\\site-packages\\keyboard\\_nixcommon.py',
   'PYMODULE'),
  ('keyboard._winkeyboard',
   'C:\\Python3\\Lib\\site-packages\\keyboard\\_winkeyboard.py',
   'PYMODULE'),
  ('PIL.Image', 'C:\\Python3\\Lib\\site-packages\\PIL\\Image.py', 'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Python3\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Python3\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Python3\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Python3\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Python3\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Python3\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Python3\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Python3\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Python3\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys', 'C:\\Python3\\Lib\\colorsys.py', 'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Python3\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Python3\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'C:\\Python3\\Lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('numpy', 'C:\\Python3\\Lib\\site-packages\\numpy\\__init__.py', 'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Python3\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Python3\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Python3\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Python3\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Python3\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Python3\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Python3\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Python3\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Python3\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('psutil',
   'C:\\Python3\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\Python3\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._compat',
   'C:\\Python3\\Lib\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\Python3\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('doctest', 'C:\\Python3\\Lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'C:\\Python3\\Lib\\pdb.py', 'PYMODULE'),
  ('code', 'C:\\Python3\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Python3\\Lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'C:\\Python3\\Lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'C:\\Python3\\Lib\\cmd.py', 'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Python3\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Python3\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Python3\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Python3\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Python3\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Python3\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Python3\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Python3\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Python3\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Python3\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Python3\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Python3\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Python3\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Python3\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Python3\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Python3\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Python3\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Python3\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Python3\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Python3\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Python3\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Python3\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Python3\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Python3\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Python3\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Python3\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Python3\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Python3\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Python3\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Python3\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Python3\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Python3\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Python3\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.compat',
   'C:\\Python3\\Lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'C:\\Python3\\Lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Python3\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('yaml', 'C:\\Python3\\Lib\\site-packages\\yaml\\__init__.py', 'PYMODULE'),
  ('yaml.cyaml', 'C:\\Python3\\Lib\\site-packages\\yaml\\cyaml.py', 'PYMODULE'),
  ('yaml.resolver',
   'C:\\Python3\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.representer',
   'C:\\Python3\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.serializer',
   'C:\\Python3\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'C:\\Python3\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.dumper',
   'C:\\Python3\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'C:\\Python3\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.loader',
   'C:\\Python3\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.composer',
   'C:\\Python3\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.parser',
   'C:\\Python3\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.scanner',
   'C:\\Python3\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.reader',
   'C:\\Python3\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.nodes', 'C:\\Python3\\Lib\\site-packages\\yaml\\nodes.py', 'PYMODULE'),
  ('yaml.events',
   'C:\\Python3\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.tokens',
   'C:\\Python3\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('yaml.error', 'C:\\Python3\\Lib\\site-packages\\yaml\\error.py', 'PYMODULE'),
  ('numpy.array_api',
   'C:\\Python3\\Lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'C:\\Python3\\Lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'C:\\Python3\\Lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Python3\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Python3\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'C:\\Python3\\Lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'C:\\Python3\\Lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'C:\\Python3\\Lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'C:\\Python3\\Lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'C:\\Python3\\Lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'C:\\Python3\\Lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'C:\\Python3\\Lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   'C:\\Python3\\Lib\\site-packages\\numpy\\array_api\\_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'C:\\Python3\\Lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'C:\\Python3\\Lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'C:\\Python3\\Lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'C:\\Python3\\Lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'C:\\Python3\\Lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Python3\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Python3\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Python3\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Python3\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Python3\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Python3\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Python3\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Python3\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Python3\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Python3\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Python3\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Python3\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Python3\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('cffi', 'C:\\Python3\\Lib\\site-packages\\cffi\\__init__.py', 'PYMODULE'),
  ('cffi.error', 'C:\\Python3\\Lib\\site-packages\\cffi\\error.py', 'PYMODULE'),
  ('cffi.api', 'C:\\Python3\\Lib\\site-packages\\cffi\\api.py', 'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Python3\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Python3\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'C:\\Python3\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Python3\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cffi.lock', 'C:\\Python3\\Lib\\site-packages\\cffi\\lock.py', 'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Python3\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Python3\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'C:\\Python3\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Python3\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Python3\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Python3\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Python3\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Python3\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pycparser',
   'C:\\Python3\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Python3\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Python3\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Python3\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Python3\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Python3\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Python3\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Python3\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Python3\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Python3\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.model', 'C:\\Python3\\Lib\\site-packages\\cffi\\model.py', 'PYMODULE'),
  ('defusedxml.ElementTree',
   'C:\\Python3\\Lib\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('defusedxml.common',
   'C:\\Python3\\Lib\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Python3\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Python3\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Python3\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Python3\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree', 'C:\\Python3\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('defusedxml',
   'C:\\Python3\\Lib\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'C:\\Python3\\Lib\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'C:\\Python3\\Lib\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'C:\\Python3\\Lib\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('xml.dom.minidom', 'C:\\Python3\\Lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'C:\\Python3\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Python3\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Python3\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Python3\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Python3\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg', 'C:\\Python3\\Lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom', 'C:\\Python3\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('defusedxml.pulldom',
   'C:\\Python3\\Lib\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'C:\\Python3\\Lib\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'C:\\Python3\\Lib\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'C:\\Python3\\Lib\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('PIL._util', 'C:\\Python3\\Lib\\site-packages\\PIL\\_util.py', 'PYMODULE'),
  ('PIL._typing',
   'C:\\Python3\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Python3\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Python3\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Python3\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Python3\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL', 'C:\\Python3\\Lib\\site-packages\\PIL\\__init__.py', 'PYMODULE'),
  ('PIL._version',
   'C:\\Python3\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('pystray',
   'C:\\Python3\\Lib\\site-packages\\pystray\\__init__.py',
   'PYMODULE'),
  ('pystray._util.win32',
   'C:\\Python3\\Lib\\site-packages\\pystray\\_util\\win32.py',
   'PYMODULE'),
  ('pystray._util.notify_dbus',
   'C:\\Python3\\Lib\\site-packages\\pystray\\_util\\notify_dbus.py',
   'PYMODULE'),
  ('pystray._util.gtk',
   'C:\\Python3\\Lib\\site-packages\\pystray\\_util\\gtk.py',
   'PYMODULE'),
  ('pystray._util',
   'C:\\Python3\\Lib\\site-packages\\pystray\\_util\\__init__.py',
   'PYMODULE'),
  ('pystray._info',
   'C:\\Python3\\Lib\\site-packages\\pystray\\_info.py',
   'PYMODULE'),
  ('pystray._base',
   'C:\\Python3\\Lib\\site-packages\\pystray\\_base.py',
   'PYMODULE'),
  ('six', 'C:\\Python3\\Lib\\site-packages\\six.py', 'PYMODULE'),
  ('pystray._xorg',
   'C:\\Python3\\Lib\\site-packages\\pystray\\_xorg.py',
   'PYMODULE'),
  ('pystray._win32',
   'C:\\Python3\\Lib\\site-packages\\pystray\\_win32.py',
   'PYMODULE'),
  ('pystray._gtk',
   'C:\\Python3\\Lib\\site-packages\\pystray\\_gtk.py',
   'PYMODULE'),
  ('pystray._darwin',
   'C:\\Python3\\Lib\\site-packages\\pystray\\_darwin.py',
   'PYMODULE'),
  ('pystray._appindicator',
   'C:\\Python3\\Lib\\site-packages\\pystray\\_appindicator.py',
   'PYMODULE'),
  ('pystray._dummy',
   'C:\\Python3\\Lib\\site-packages\\pystray\\_dummy.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Python3\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Python3\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.ttk', 'C:\\Python3\\Lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('tkinter', 'C:\\Python3\\Lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.constants', 'C:\\Python3\\Lib\\tkinter\\constants.py', 'PYMODULE')],
 [('python312.dll', 'C:\\Python3\\python312.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'C:\\Python3\\Lib\\site-packages\\numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'BINARY'),
  ('_decimal.pyd', 'C:\\Python3\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Python3\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Python3\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Python3\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'C:\\Python3\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\Python3\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\Python3\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'C:\\Python3\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Python3\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Python3\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'C:\\Python3\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Python3\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('win32\\win32evtlog.pyd',
   'C:\\Python3\\Lib\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Python3\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('_wmi.pyd', 'C:\\Python3\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('yarl\\_quoting_c.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\yarl\\_quoting_c.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('multidict\\_multidict.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\multidict\\_multidict.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_asyncio.pyd', 'C:\\Python3\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('aiohttp\\_http_writer.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\_http_writer.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp\\_http_parser.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\_http_parser.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('frozenlist\\_frozenlist.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\frozenlist\\_frozenlist.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp\\_helpers.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\_helpers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp\\_websocket.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\aiohttp\\_websocket.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'C:\\Python3\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'C:\\Python3\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('win32\\win32gui.pyd',
   'C:\\Python3\\Lib\\site-packages\\win32\\win32gui.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\sip.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\PIL\\_webp.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\PIL\\_imagingtk.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\PIL\\_imagingcms.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\_multiarray_tests.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\numpy\\core\\_multiarray_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Python3\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\Python3\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\numpy\\random\\_philox.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\numpy\\random\\_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\numpy\\random\\_common.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\yaml\\_yaml.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\PIL\\_imagingmath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\_cffi_backend.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp312-win_amd64.pyd',
   'C:\\Python3\\Lib\\site-packages\\PIL\\_imaging.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd', 'C:\\Python3\\DLLs\\_elementtree.pyd', 'EXTENSION'),
  ('_tkinter.pyd', 'C:\\Python3\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll', 'C:\\Python3\\VCRUNTIME140.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\Python3\\VCRUNTIME140_1.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('libcrypto-3.dll', 'C:\\Python3\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libffi-8.dll', 'C:\\Python3\\DLLs\\libffi-8.dll', 'BINARY'),
  ('libssl-3.dll', 'C:\\Python3\\DLLs\\libssl-3.dll', 'BINARY'),
  ('pywin32_system32\\pywintypes312.dll',
   'C:\\Python3\\Lib\\site-packages\\pywin32_system32\\pywintypes312.dll',
   'BINARY'),
  ('python3.dll', 'C:\\Python3\\python3.dll', 'BINARY'),
  ('tk86t.dll', 'C:\\Python3\\DLLs\\tk86t.dll', 'BINARY'),
  ('tcl86t.dll', 'C:\\Python3\\DLLs\\tcl86t.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('zlib1.dll', 'C:\\Python3\\DLLs\\zlib1.dll', 'BINARY')],
 [],
 [],
 [('config.json', 'D:\\test\\aiai\\config.json', 'DATA'),
  ('img\\Google AI Studio.ico',
   'D:\\test\\aiai\\img\\Google AI Studio.ico',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\Python3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'C:\\Python3\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\pt.msg', 'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\be.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tk_data\\iconlist.tcl', 'C:\\Python3\\tcl\\tk8.6\\iconlist.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\kl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\et.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg', 'C:\\Python3\\tcl\\tk8.6\\msgs\\cs.msg', 'DATA'),
  ('_tcl_data\\msgs\\ca.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\ca.msg', 'DATA'),
  ('_tcl_data\\msgs\\eo.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\eo.msg', 'DATA'),
  ('_tk_data\\tclIndex', 'C:\\Python3\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'C:\\Python3\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg', 'C:\\Python3\\tcl\\tk8.6\\msgs\\ru.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\af.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Cuba', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Cuba', 'DATA'),
  ('_tcl_data\\msgs\\da.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\da.msg', 'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'C:\\Python3\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'C:\\Python3\\tcl\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\EST', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\EST', 'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'C:\\Python3\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Iran', 'DATA'),
  ('_tcl_data\\msgs\\it.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\it.msg', 'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tk_data\\comdlg.tcl', 'C:\\Python3\\tcl\\tk8.6\\comdlg.tcl', 'DATA'),
  ('_tk_data\\msgs\\es.msg', 'C:\\Python3\\tcl\\tk8.6\\msgs\\es.msg', 'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tk_data\\msgs\\da.msg', 'C:\\Python3\\tcl\\tk8.6\\msgs\\da.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\NZ', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tm.tcl', 'C:\\Python3\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\ko.msg', 'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\hu.msg', 'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\bn.msg', 'DATA'),
  ('_tcl_data\\msgs\\mr.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\mr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tk_data\\button.tcl', 'C:\\Python3\\tcl\\tk8.6\\button.tcl', 'DATA'),
  ('_tcl_data\\history.tcl', 'C:\\Python3\\tcl\\tcl8.6\\history.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'C:\\Python3\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\GMT', 'DATA'),
  ('_tcl_data\\msgs\\ms.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\ms.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tk_data\\bgerror.tcl', 'C:\\Python3\\tcl\\tk8.6\\bgerror.tcl', 'DATA'),
  ('_tcl_data\\safe.tcl', 'C:\\Python3\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\th.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tk_data\\tkfbox.tcl', 'C:\\Python3\\tcl\\tk8.6\\tkfbox.tcl', 'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'C:\\Python3\\tcl\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'C:\\Python3\\tcl\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'C:\\Python3\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\cs.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'C:\\Python3\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\id.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tk_data\\menu.tcl', 'C:\\Python3\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tk_data\\entry.tcl', 'C:\\Python3\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\bg.msg', 'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg', 'C:\\Python3\\tcl\\tk8.6\\msgs\\hu.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\hi.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg', 'C:\\Python3\\tcl\\tk8.6\\msgs\\pt.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\init.tcl', 'C:\\Python3\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\nl.msg', 'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'C:\\Python3\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'C:\\Python3\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl', 'C:\\Python3\\tcl\\tk8.6\\ttk\\ttk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tk_data\\icons.tcl', 'C:\\Python3\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\CET', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\CET', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\mt.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\he.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\sl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\ro.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\fa.msg', 'DATA'),
  ('_tcl_data\\msgs\\lv.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\lv.msg', 'DATA'),
  ('_tcl_data\\tzdata\\ROK', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\ROK', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tk_data\\license.terms', 'C:\\Python3\\tcl\\tk8.6\\license.terms', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\ROC', 'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tk_data\\tearoff.tcl', 'C:\\Python3\\tcl\\tk8.6\\tearoff.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tk_data\\tk.tcl', 'C:\\Python3\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg', 'C:\\Python3\\tcl\\tk8.6\\msgs\\fr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg', 'C:\\Python3\\tcl\\tk8.6\\msgs\\pl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\pl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\ja.msg', 'DATA'),
  ('_tcl_data\\msgs\\tr.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\tr.msg', 'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\MET', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\MET', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg', 'C:\\Python3\\tcl\\tk8.6\\msgs\\nl.msg', 'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg', 'C:\\Python3\\tcl\\tk8.6\\msgs\\sv.msg', 'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\sr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'C:\\Python3\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tclIndex', 'C:\\Python3\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\nb.msg', 'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'C:\\Python3\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\EET', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\EET', 'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Zulu', 'DATA'),
  ('_tk_data\\listbox.tcl', 'C:\\Python3\\tcl\\tk8.6\\listbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\vi.msg', 'DATA'),
  ('_tk_data\\msgs\\en.msg', 'C:\\Python3\\tcl\\tk8.6\\msgs\\en.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\MST', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\MST', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tk_data\\choosedir.tcl', 'C:\\Python3\\tcl\\tk8.6\\choosedir.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tk_data\\images\\README',
   'C:\\Python3\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'C:\\Python3\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'C:\\Python3\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tk_data\\text.tcl', 'C:\\Python3\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\fo.msg', 'DATA'),
  ('_tk_data\\msgs\\de.msg', 'C:\\Python3\\tcl\\tk8.6\\msgs\\de.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\ga.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg', 'C:\\Python3\\tcl\\tk8.6\\msgs\\fi.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\kw.msg', 'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\lt.msg', 'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tk_data\\msgbox.tcl', 'C:\\Python3\\tcl\\tk8.6\\msgbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\GB', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\GB', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tk_data\\xmfbox.tcl', 'C:\\Python3\\tcl\\tk8.6\\xmfbox.tcl', 'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tk_data\\clrpick.tcl', 'C:\\Python3\\tcl\\tk8.6\\clrpick.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\ar.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:\\Python3\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\clock.tcl', 'C:\\Python3\\tcl\\tcl8.6\\clock.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\hr.msg', 'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tk_data\\dialog.tcl', 'C:\\Python3\\tcl\\tk8.6\\dialog.tcl', 'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.5.tm',
   'C:\\Python3\\tcl\\tcl8\\8.5\\tcltest-2.5.5.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tk_data\\palette.tcl', 'C:\\Python3\\tcl\\tk8.6\\palette.tcl', 'DATA'),
  ('_tcl_data\\msgs\\ta.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\ta.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\eu.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\el.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tk_data\\scale.tcl', 'C:\\Python3\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tk_data\\obsolete.tcl', 'C:\\Python3\\tcl\\tk8.6\\obsolete.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\WET', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\WET', 'DATA'),
  ('_tcl_data\\msgs\\zh.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\zh.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\ru.msg', 'DATA'),
  ('_tcl_data\\package.tcl', 'C:\\Python3\\tcl\\tcl8.6\\package.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\nn.msg', 'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\sv.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tk_data\\msgs\\el.msg', 'C:\\Python3\\tcl\\tk8.6\\msgs\\el.msg', 'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\es.msg', 'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl', 'C:\\Python3\\tcl\\tk8.6\\pkgIndex.tcl', 'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tk_data\\spinbox.tcl', 'C:\\Python3\\tcl\\tk8.6\\spinbox.tcl', 'DATA'),
  ('_tcl_data\\msgs\\te.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\te.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tk_data\\msgs\\it.msg', 'C:\\Python3\\tcl\\tk8.6\\msgs\\it.msg', 'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'C:\\Python3\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tk_data\\optMenu.tcl', 'C:\\Python3\\tcl\\tk8.6\\optMenu.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tk_data\\safetk.tcl', 'C:\\Python3\\tcl\\tk8.6\\safetk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'C:\\Python3\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\sq.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Eire', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\parray.tcl', 'C:\\Python3\\tcl\\tcl8.6\\parray.tcl', 'DATA'),
  ('_tcl_data\\msgs\\sw.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\sw.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\UTC', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'C:\\Python3\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\auto.tcl', 'C:\\Python3\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\mk.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\word.tcl', 'C:\\Python3\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'C:\\Python3\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg', 'C:\\Python3\\tcl\\tk8.6\\msgs\\eo.msg', 'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\HST', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\HST', 'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tk_data\\console.tcl', 'C:\\Python3\\tcl\\tk8.6\\console.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\UCT', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\UCT', 'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'C:\\Python3\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\gv.msg', 'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'C:\\Python3\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\fi.msg', 'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tk_data\\scrlbar.tcl', 'C:\\Python3\\tcl\\tk8.6\\scrlbar.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\W-SU', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\W-SU', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\uk.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\is.msg', 'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'C:\\Python3\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tk_data\\focus.tcl', 'C:\\Python3\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\sk.msg', 'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:\\Python3\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\GMT0', 'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'C:\\Python3\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\de.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'C:\\Python3\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'C:\\Python3\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\fr.msg', 'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\sh.msg', 'DATA'),
  ('_tk_data\\mkpsenc.tcl', 'C:\\Python3\\tcl\\tk8.6\\mkpsenc.tcl', 'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC', 'C:\\Python3\\tcl\\tcl8.6\\tzdata\\PRC', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg', 'C:\\Python3\\tcl\\tcl8.6\\msgs\\gl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'C:\\Python3\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'C:\\Python3\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\RECORD',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\RECORD',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'DATA'),
  ('attrs-24.2.0.dist-info\\REQUESTED',
   'C:\\Python3\\Lib\\site-packages\\attrs-24.2.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\WHEEL',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\METADATA',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\METADATA',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\INSTALLER',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\INSTALLER',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\INSTALLER',
   'DATA'),
  ('attrs-24.2.0.dist-info\\licenses\\LICENSE',
   'C:\\Python3\\Lib\\site-packages\\attrs-24.2.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('attrs-24.2.0.dist-info\\INSTALLER',
   'C:\\Python3\\Lib\\site-packages\\attrs-24.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\WHEEL',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\WHEEL',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\top_level.txt',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\top_level.txt',
   'DATA'),
  ('attrs-24.2.0.dist-info\\METADATA',
   'C:\\Python3\\Lib\\site-packages\\attrs-24.2.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\REQUESTED',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\RECORD',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\RECORD',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\METADATA',
   'C:\\Python3\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\LICENSE.txt',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('attrs-24.2.0.dist-info\\WHEEL',
   'C:\\Python3\\Lib\\site-packages\\attrs-24.2.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\entry_points.txt',
   'C:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\entry_points.txt',
   'DATA'),
  ('attrs-24.2.0.dist-info\\RECORD',
   'C:\\Python3\\Lib\\site-packages\\attrs-24.2.0.dist-info\\RECORD',
   'DATA'),
  ('base_library.zip',
   'D:\\test\\aiai\\build\\ai_assistant\\base_library.zip',
   'DATA')])
