import tkinter as tk
from tkinter import ttk, messagebox
import json
import os

class SettingsWindow(tk.Toplevel):
    def __init__(self, parent):
        super().__init__(parent)
        self.title("设置")
        self.geometry("500x600")  # 增加窗口高度以适应所有内容
        self.resizable(False, False)
        
        # 加载配置
        self.config = self.load_config()
        
        self.setup_ui()
        
        # 设置窗口位置为父窗口中心
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - (width // 2)
        y = parent.winfo_y() + (parent.winfo_height() // 2) - (height // 2)
        self.geometry(f'+{x}+{y}')
        
    def setup_ui(self):
        # 创建notebook用于分页显示设置
        notebook = ttk.Notebook(self)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # === API设置页面 ===
        api_frame = ttk.Frame(notebook)
        notebook.add(api_frame, text="API设置")
        
        # 创建滚动框架
        canvas = tk.Canvas(api_frame)
        scrollbar = ttk.Scrollbar(api_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 模型选择
        ttk.Label(scrollable_frame, text="当前使用模型:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.model_var = tk.StringVar(value=self.config.get("selected_model", "DeepSeek"))
        model_combo = ttk.Combobox(scrollable_frame, textvariable=self.model_var, 
                                 values=["DeepSeek", "Kimi", "ChatGPT", "QianWen"])
        model_combo.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        model_combo.bind("<<ComboboxSelected>>", self.on_model_change)
        
        # API Keys
        ttk.Label(scrollable_frame, text="DeepSeek API Key:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.deepseek_key = ttk.Entry(scrollable_frame, show="*", width=40)
        self.deepseek_key.insert(0, self.config.get("deepseek_key", ""))
        self.deepseek_key.grid(row=1, column=1, padx=5, pady=5, sticky="ew")
        
        ttk.Label(scrollable_frame, text="Kimi API Key:").grid(row=2, column=0, padx=5, pady=5, sticky="w")
        self.kimi_key = ttk.Entry(scrollable_frame, show="*", width=40)
        self.kimi_key.insert(0, self.config.get("kimi_key", ""))
        self.kimi_key.grid(row=2, column=1, padx=5, pady=5, sticky="ew")
        
        ttk.Label(scrollable_frame, text="ChatGPT API Key:").grid(row=3, column=0, padx=5, pady=5, sticky="w")
        self.chatgpt_key = ttk.Entry(scrollable_frame, show="*", width=40)
        self.chatgpt_key.insert(0, self.config.get("chatgpt_key", ""))
        self.chatgpt_key.grid(row=3, column=1, padx=5, pady=5, sticky="ew")
        
        ttk.Label(scrollable_frame, text="QianWen API Key:").grid(row=4, column=0, padx=5, pady=5, sticky="w")
        self.qianwen_key = ttk.Entry(scrollable_frame, show="*", width=40)
        self.qianwen_key.insert(0, self.config.get("qianwen_key", ""))
        self.qianwen_key.grid(row=4, column=1, padx=5, pady=5, sticky="ew")
        
        # 模型设置
        ttk.Label(scrollable_frame, text="DeepSeek 模型:").grid(row=5, column=0, padx=5, pady=5, sticky="w")
        self.deepseek_model = ttk.Entry(scrollable_frame, width=40)
        self.deepseek_model.insert(0, self.config.get("deepseek_model", "deepseek-chat"))
        self.deepseek_model.grid(row=5, column=1, padx=5, pady=5, sticky="ew")
        
        ttk.Label(scrollable_frame, text="Kimi 模型:").grid(row=6, column=0, padx=5, pady=5, sticky="w")
        self.kimi_model = ttk.Entry(scrollable_frame, width=40)
        self.kimi_model.insert(0, self.config.get("kimi_model", "moonshot-v1-8k"))
        self.kimi_model.grid(row=6, column=1, padx=5, pady=5, sticky="ew")
        
        ttk.Label(scrollable_frame, text="ChatGPT 模型:").grid(row=7, column=0, padx=5, pady=5, sticky="w")
        self.chatgpt_model = ttk.Entry(scrollable_frame, width=40)
        self.chatgpt_model.insert(0, self.config.get("chatgpt_model", "gpt-4"))
        self.chatgpt_model.grid(row=7, column=1, padx=5, pady=5, sticky="ew")
        
        ttk.Label(scrollable_frame, text="QianWen 模型:").grid(row=8, column=0, padx=5, pady=5, sticky="w")
        self.qianwen_model = ttk.Entry(scrollable_frame, width=40)
        self.qianwen_model.insert(0, self.config.get("qianwen_model", "qwen-plus"))
        self.qianwen_model.grid(row=8, column=1, padx=5, pady=5, sticky="ew")

        # API Base URLs
        ttk.Label(scrollable_frame, text="DeepSeek API URL:").grid(row=9, column=0, padx=5, pady=5, sticky="w")
        self.deepseek_url = ttk.Entry(scrollable_frame, width=40)
        self.deepseek_url.insert(0, self.config.get("deepseek_url", "https://api.deepseek.com/v1/chat/completions"))
        self.deepseek_url.grid(row=9, column=1, padx=5, pady=5, sticky="ew")
        
        ttk.Label(scrollable_frame, text="Kimi API URL:").grid(row=10, column=0, padx=5, pady=5, sticky="w")
        self.kimi_url = ttk.Entry(scrollable_frame, width=40)
        self.kimi_url.insert(0, self.config.get("kimi_url", "https://api.moonshot.cn/v1/chat/completions"))
        self.kimi_url.grid(row=10, column=1, padx=5, pady=5, sticky="ew")
        
        ttk.Label(scrollable_frame, text="ChatGPT API URL:").grid(row=11, column=0, padx=5, pady=5, sticky="w")
        self.chatgpt_url = ttk.Entry(scrollable_frame, width=40)
        self.chatgpt_url.insert(0, self.config.get("chatgpt_url", "https://api.openai.com/v1/chat/completions"))
        self.chatgpt_url.grid(row=11, column=1, padx=5, pady=5, sticky="ew")
        
        ttk.Label(scrollable_frame, text="QianWen API URL:").grid(row=12, column=0, padx=5, pady=5, sticky="w")
        self.qianwen_url = ttk.Entry(scrollable_frame, width=40)
        self.qianwen_url.insert(0, self.config.get("qianwen_url", "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"))
        self.qianwen_url.grid(row=12, column=1, padx=5, pady=5, sticky="ew")

        # 代理设置
        ttk.Label(scrollable_frame, text="HTTP代理:").grid(row=13, column=0, padx=5, pady=5, sticky="w")
        self.http_proxy = ttk.Entry(scrollable_frame, width=40)
        self.http_proxy.insert(0, self.config.get("http_proxy", ""))
        self.http_proxy.grid(row=13, column=1, padx=5, pady=5, sticky="ew")
        
        ttk.Label(scrollable_frame, text="HTTPS代理:").grid(row=14, column=0, padx=5, pady=5, sticky="w")
        self.https_proxy = ttk.Entry(scrollable_frame, width=40)
        self.https_proxy.insert(0, self.config.get("https_proxy", ""))
        self.https_proxy.grid(row=14, column=1, padx=5, pady=5, sticky="ew")

        # 保存按钮
        ttk.Button(scrollable_frame, text="保存设置", command=self.save_settings).grid(row=15, column=0, columnspan=2, pady=20)
        
        # 配置网格列权重
        scrollable_frame.grid_columnconfigure(1, weight=1)
        
        # 打包滚动条和画布
        scrollbar.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)
        
        # === 提示词设置页面 ===
        prompt_frame = ttk.Frame(notebook)
        notebook.add(prompt_frame, text="提示词设置")
        
        # 提示词1
        ttk.Label(prompt_frame, text="提示词1:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.prompt1 = tk.Text(prompt_frame, height=3, width=40, wrap=tk.WORD)
        self.prompt1.insert("1.0", self.config.get("prompt1", ""))
        self.prompt1.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        
        # 提示词2
        ttk.Label(prompt_frame, text="提示词2:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.prompt2 = tk.Text(prompt_frame, height=3, width=40, wrap=tk.WORD)
        self.prompt2.insert("1.0", self.config.get("prompt2", ""))
        self.prompt2.grid(row=1, column=1, padx=5, pady=5, sticky="ew")
        
        # 提示词3
        ttk.Label(prompt_frame, text="提示词3:").grid(row=2, column=0, padx=5, pady=5, sticky="w")
        self.prompt3 = tk.Text(prompt_frame, height=3, width=40, wrap=tk.WORD)
        self.prompt3.insert("1.0", self.config.get("prompt3", ""))
        self.prompt3.grid(row=2, column=1, padx=5, pady=5, sticky="ew")
        
        # 提示词4
        ttk.Label(prompt_frame, text="提示词4:").grid(row=3, column=0, padx=5, pady=5, sticky="w")
        self.prompt4 = tk.Text(prompt_frame, height=3, width=40, wrap=tk.WORD)
        self.prompt4.insert("1.0", self.config.get("prompt4", ""))
        self.prompt4.grid(row=3, column=1, padx=5, pady=5, sticky="ew")
        
        # 保存按钮
        ttk.Button(prompt_frame, text="保存设置", command=self.save_settings).grid(row=4, column=0, columnspan=2, pady=20)
        
        # 配置网格列权重
        prompt_frame.grid_columnconfigure(1, weight=1)
        
    def on_model_change(self, event):
        # 可以在这里添加模型切换时的特殊处理
        pass
        
    def save_settings(self):
        config = {
            "selected_model": self.model_var.get(),
            "deepseek_key": self.deepseek_key.get(),
            "kimi_key": self.kimi_key.get(),
            "chatgpt_key": self.chatgpt_key.get(),
            "qianwen_key": self.qianwen_key.get(),
            "deepseek_model": self.deepseek_model.get(),
            "kimi_model": self.kimi_model.get(),
            "chatgpt_model": self.chatgpt_model.get(),
            "qianwen_model": self.qianwen_model.get(),
            "deepseek_url": self.deepseek_url.get(),
            "kimi_url": self.kimi_url.get(),
            "chatgpt_url": self.chatgpt_url.get(),
            "qianwen_url": self.qianwen_url.get(),
            "http_proxy": self.http_proxy.get(),
            "https_proxy": self.https_proxy.get(),
            "prompt1": self.prompt1.get("1.0", tk.END).strip(),
            "prompt2": self.prompt2.get("1.0", tk.END).strip(),
            "prompt3": self.prompt3.get("1.0", tk.END).strip(),
            "prompt4": self.prompt4.get("1.0", tk.END).strip()
        }
        
        self.save_config(config)
        messagebox.showinfo("成功", "设置已保存")
        self.destroy()
        
    @staticmethod
    def load_config():
        config_path = os.path.join(os.path.dirname(__file__), "config.json")
        if os.path.exists(config_path):
            try:
                with open(config_path, "r", encoding="utf-8") as f:
                    return json.load(f)
            except:
                return {}
        return {}
        
    @staticmethod
    def save_config(config):
        config_path = os.path.join(os.path.dirname(__file__), "config.json")
        with open(config_path, "w", encoding="utf-8") as f:
            json.dump(config, f, indent=4, ensure_ascii=False)